# ACME Relay Configuration File

# Server Configuration
server:
  listen_addr: "127.0.0.1:6060"
  read_header_timeout: "10s"
  endpoint_path: "/getcert"

# ACME Configuration
acme:
  # Required: Account email for ACME registration
  account_email: "<EMAIL>"

  # Data directory for storing certificates and account information
  data_dir: "./data"

  # ACME directory URL (empty for Let's Encrypt production)
  # For staging: https://acme-staging-v02.api.letsencrypt.org/directory
  directory_url: ""

  # Certificate renewal settings (30 days)
  renew_before: "720h"

  # Certificate key type (EC256, EC384, RSA2048, RSA4096, RSA8192)
  key_type: "EC256"


